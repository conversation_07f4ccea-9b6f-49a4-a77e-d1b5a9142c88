#!/usr/bin/env python3
"""
Script de teste para verificar a diferença entre usar OHL vs OHLC
nos classificadores XGBoost, Butterworth e Moving Averages

Autor: Assistente IA
Data: 2025-07-09
"""

import yfinance as yf
import pandas as pd
import numpy as np
import os
import sys
from datetime import datetime, timedelta

# Adicionar o diretório src ao path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config_loader import ConfigLoader

def baixar_dados_teste(ticker='PETR4.SA', period='6mo'):
    """
    Baixa dados de teste para uma ação
    """
    try:
        print(f"📊 Baixando dados de {ticker} para teste...")
        dados = yf.download(ticker, period=period, progress=False)
        
        if dados.empty:
            print(f"❌ Nenhum dado encontrado para {ticker}")
            return None
            
        # Resetar índice para ter Date como coluna
        dados = dados.reset_index()
        
        print(f"✅ Dados obtidos: {len(dados)} dias")
        return dados
        
    except Exception as e:
        print(f"❌ Erro ao baixar dados de {ticker}: {e}")
        return None

def testar_calculo_medias(dados):
    """
    Testa o cálculo das médias com diferentes configurações
    """
    print("\n" + "="*60)
    print("🧪 TESTE: Cálculo das Médias OHL vs OHLC")
    print("="*60)
    
    # Calcular média OHLC (tradicional)
    dados['Media_OHLC_Tradicional'] = (dados['Open'] + dados['High'] + dados['Low'] + dados['Close']) / 4
    
    # Calcular média OHL (sem Close)
    dados['Media_OHL'] = (dados['Open'] + dados['High'] + dados['Low']) / 3
    
    # Calcular diferença
    dados['Diferenca_Absoluta'] = abs(dados['Media_OHLC_Tradicional'] - dados['Media_OHL'])
    dados['Diferenca_Percentual'] = (dados['Diferenca_Absoluta'] / dados['Media_OHLC_Tradicional']) * 100
    
    # Estatísticas
    print(f"📈 Últimos 10 dias de dados:")
    print(f"{'Data':<12} {'OHLC':<8} {'OHL':<8} {'Diff %':<8}")
    print("-" * 40)
    
    for i in range(max(0, len(dados)-10), len(dados)):
        data_val = dados.iloc[i]['Date']
        if hasattr(data_val, 'strftime'):
            data = data_val.strftime('%Y-%m-%d')
        else:
            data = str(data_val)[:10]  # Pegar apenas a parte da data
        ohlc = dados.iloc[i]['Media_OHLC_Tradicional']
        ohl = dados.iloc[i]['Media_OHL']
        diff_pct = dados.iloc[i]['Diferenca_Percentual']
        print(f"{data:<12} {ohlc:<8.2f} {ohl:<8.2f} {diff_pct:<8.2f}")
    
    print(f"\n📊 Estatísticas da diferença:")
    print(f"   Diferença média: {dados['Diferenca_Percentual'].mean():.4f}%")
    print(f"   Diferença máxima: {dados['Diferenca_Percentual'].max():.4f}%")
    print(f"   Diferença mínima: {dados['Diferenca_Percentual'].min():.4f}%")
    print(f"   Desvio padrão: {dados['Diferenca_Percentual'].std():.4f}%")
    
    return dados

def testar_configuracao_ohl():
    """
    Testa a configuração use_ohl_only
    """
    print("\n" + "="*60)
    print("⚙️ TESTE: Configuração use_ohl_only")
    print("="*60)
    
    config = ConfigLoader()
    
    # Testar configuração atual
    use_ohlc = config.get('moving_averages.use_ohlc_average')
    use_ohl_only = config.get('moving_averages.use_ohl_only')
    
    print(f"📋 Configurações atuais:")
    print(f"   use_ohlc_average: {use_ohlc}")
    print(f"   use_ohl_only: {use_ohl_only}")
    
    # Simular cálculo baseado na configuração
    dados_teste = baixar_dados_teste('VALE3.SA', '1mo')
    if dados_teste is not None:
        if use_ohl_only:
            media_calculada = (dados_teste['Open'] + dados_teste['High'] + dados_teste['Low']) / 3
            print(f"✅ Usando média OHL (sem Close)")
        elif use_ohlc:
            media_calculada = (dados_teste['Open'] + dados_teste['Close'] + dados_teste['Low'] + dados_teste['High']) / 4
            print(f"✅ Usando média OHLC (com Close)")
        else:
            media_calculada = dados_teste['Close']
            print(f"✅ Usando apenas Close")
        
        print(f"📊 Últimos 3 valores calculados:")
        for i in range(max(0, len(media_calculada)-3), len(media_calculada)):
            data_val = dados_teste.iloc[i]['Date']
            if hasattr(data_val, 'strftime'):
                data = data_val.strftime('%Y-%m-%d')
            else:
                data = str(data_val)[:10]  # Pegar apenas a parte da data
            valor = media_calculada.iloc[i]
            print(f"   {data}: {valor:.2f}")

def testar_features_econometricas():
    """
    Testa se as features econométricas estão usando a configuração correta
    """
    print("\n" + "="*60)
    print("🔬 TESTE: Features Econométricas com OHL")
    print("="*60)
    
    # Importar função de cálculo de features
    try:
        from classificador_xgboost_sinais import calcular_features_econometricas_ohlcv
        
        dados_teste = baixar_dados_teste('ITUB4.SA', '3mo')
        if dados_teste is not None:
            print("🧮 Calculando features econométricas...")
            dados_com_features = calcular_features_econometricas_ohlcv(dados_teste)
            
            # Verificar se as features foram calculadas
            features_econometricas = [
                'Parkinson_Volatility', 'MFI', 'EMV', 'Amihud', 
                'Roll_Spread', 'Hurst', 'Vol_per_Volume', 'CMF', 'AD_Line', 'VO'
            ]
            
            print("📊 Features calculadas:")
            for feature in features_econometricas:
                if feature in dados_com_features.columns:
                    valor_atual = dados_com_features[feature].iloc[-1]
                    if not pd.isna(valor_atual):
                        print(f"   ✅ {feature}: {valor_atual:.6f}")
                    else:
                        print(f"   ⚠️ {feature}: NaN")
                else:
                    print(f"   ❌ {feature}: Não encontrada")
                    
    except ImportError as e:
        print(f"❌ Erro ao importar função: {e}")
    except Exception as e:
        print(f"❌ Erro ao calcular features: {e}")

def main():
    """
    Função principal de teste
    """
    print("🚀 INICIANDO TESTES OHL vs OHLC")
    print("="*60)
    
    # Teste 1: Baixar dados e calcular diferenças
    dados_teste = baixar_dados_teste('PETR4.SA', '6mo')
    if dados_teste is not None:
        dados_com_medias = testar_calculo_medias(dados_teste)
    
    # Teste 2: Verificar configuração
    testar_configuracao_ohl()
    
    # Teste 3: Testar features econométricas
    testar_features_econometricas()
    
    print("\n" + "="*60)
    print("✅ TESTES CONCLUÍDOS")
    print("="*60)
    
    print("\n📝 RESUMO:")
    print("   - As modificações permitem usar OHL em vez de OHLC")
    print("   - Configuração use_ohl_only controla o comportamento")
    print("   - Features econométricas adaptadas para usar price_reference")
    print("   - Ideal para trading intraday quando Close não está disponível")

if __name__ == "__main__":
    main()
