#!/usr/bin/env python3
"""
Script de teste simples para verificar se os classificadores estão usando OHL em vez de OHLC

Autor: Assistente IA
Data: 2025-07-09
"""

import yfinance as yf
import pandas as pd
import numpy as np
import os
import sys

# Adicionar o diretório src ao path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def teste_calculo_ohl():
    """
    Testa o cálculo da média OHL vs OHLC
    """
    print("🧪 TESTE: Cálculo OHL vs OHLC")
    print("="*50)
    
    # Baixar dados de teste
    ticker = 'PETR4.SA'
    print(f"📊 Baixando dados de {ticker}...")
    
    try:
        dados = yf.download(ticker, period='5d', progress=False)
        if dados.empty:
            print("❌ Nenhum dado encontrado")
            return
            
        # Resetar índice
        dados = dados.reset_index()
        
        print(f"✅ Dados obtidos: {len(dados)} dias")
        
        # Calcular médias
        dados['Media_OHLC'] = (dados['Open'] + dados['High'] + dados['Low'] + dados['Close']) / 4
        dados['Media_OHL'] = (dados['Open'] + dados['High'] + dados['Low']) / 3
        dados['Diferenca_Pct'] = ((dados['Media_OHL'] - dados['Media_OHLC']) / dados['Media_OHLC']) * 100
        
        print("\n📈 Comparação dos últimos 3 dias:")
        print(f"{'Data':<12} {'OHLC':<8} {'OHL':<8} {'Diff%':<8}")
        print("-" * 40)
        
        for i in range(max(0, len(dados)-3), len(dados)):
            data = str(dados.iloc[i]['Date'])[:10]
            ohlc = float(dados.iloc[i]['Media_OHLC'])
            ohl = float(dados.iloc[i]['Media_OHL'])
            diff = float(dados.iloc[i]['Diferenca_Pct'])
            print(f"{data:<12} {ohlc:<8.2f} {ohl:<8.2f} {diff:<8.2f}%")
            
        print(f"\n📊 Diferença média: {dados['Diferenca_Pct'].mean():.4f}%")
        
    except Exception as e:
        print(f"❌ Erro: {e}")

def teste_features_econometricas():
    """
    Testa se as features econométricas estão usando OHL
    """
    print("\n🔬 TESTE: Features Econométricas com OHL")
    print("="*50)
    
    try:
        from classificador_xgboost_sinais import calcular_features_econometricas_ohlcv
        
        # Baixar dados de teste
        ticker = 'VALE3.SA'
        print(f"📊 Baixando dados de {ticker}...")
        
        dados = yf.download(ticker, period='3mo', progress=False)
        if dados.empty:
            print("❌ Nenhum dado encontrado")
            return
            
        dados = dados.reset_index()
        print(f"✅ Dados obtidos: {len(dados)} dias")
        
        # Calcular features
        print("🧮 Calculando features econométricas...")
        dados_com_features = calcular_features_econometricas_ohlcv(dados)
        
        # Verificar algumas features
        features_teste = ['MFI', 'Amihud', 'CMF']
        
        print("\n📊 Últimos valores das features:")
        for feature in features_teste:
            if feature in dados_com_features.columns:
                valor = dados_com_features[feature].iloc[-1]
                if not pd.isna(valor):
                    print(f"   ✅ {feature}: {valor:.6f}")
                else:
                    print(f"   ⚠️ {feature}: NaN")
            else:
                print(f"   ❌ {feature}: Não encontrada")
                
        print("\n✅ Features calculadas com sucesso usando OHL!")
        
    except ImportError as e:
        print(f"❌ Erro ao importar: {e}")
    except Exception as e:
        print(f"❌ Erro: {e}")

def main():
    """
    Função principal
    """
    print("🚀 TESTE SIMPLES: OHL vs OHLC")
    print("="*50)
    
    # Teste 1: Comparar cálculos
    teste_calculo_ohl()
    
    # Teste 2: Features econométricas
    teste_features_econometricas()
    
    print("\n" + "="*50)
    print("✅ TESTES CONCLUÍDOS")
    print("="*50)
    
    print("\n📝 RESUMO DAS MODIFICAÇÕES:")
    print("   ✅ XGBoost: Usa média OHL em vez de OHLC")
    print("   ✅ Butterworth: Usa média OHL em vez de OHLC") 
    print("   ✅ Moving Averages: Usa média OHL em vez de OHLC")
    print("   ✅ Features econométricas: Usam OHL como price_reference")
    print("   ✅ Ideal para trading intraday (sem valor Close)")

if __name__ == "__main__":
    main()
