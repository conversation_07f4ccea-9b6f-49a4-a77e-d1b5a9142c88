#!/usr/bin/env python3
"""
Script para verificar como a feature Last_Day_Quarter está funcionando
Mostra exemplos de datas que são marcadas como último dia de quarter
"""

import pandas as pd
import numpy as np
import os
from datetime import datetime

def verificar_last_day_quarter():
    """
    Verifica como a feature Last_Day_Quarter está funcionando nos dados
    """
    
    print("📅 VERIFICAÇÃO DA FEATURE LAST_DAY_QUARTER")
    print("=" * 60)
    
    # Carregar dados de uma ação para verificar
    csv_path = 'results/csv/xgboost_analysis/individual_stocks/xgboost_NTCO3.csv'
    
    if not os.path.exists(csv_path):
        print(f"❌ Arquivo não encontrado: {csv_path}")
        return
    
    # Carregar dados
    dados = pd.read_csv(csv_path)
    dados['Data'] = pd.to_datetime(dados['Data'])
    dados = dados.set_index('Data')
    
    print(f"📊 Dados carregados: {len(dados)} registros")
    print(f"📅 Período: {dados.index.min().strftime('%Y-%m-%d')} a {dados.index.max().strftime('%Y-%m-%d')}")
    print()
    
    # Verificar se a coluna Last_Day_Quarter existe
    if 'Last_Day_Quarter' not in dados.columns:
        print("❌ Coluna Last_Day_Quarter não encontrada nos dados")
        return
    
    # Encontrar todos os dias marcados como último dia de quarter
    last_days = dados[dados['Last_Day_Quarter'] == 1].copy()
    
    print(f"🎯 DIAS MARCADOS COMO ÚLTIMO DIA DE QUARTER: {len(last_days)}")
    print("-" * 60)
    
    if len(last_days) == 0:
        print("❌ Nenhum dia foi marcado como último dia de quarter")
        return
    
    # Agrupar por ano e quarter para análise
    last_days['year'] = last_days.index.year
    last_days['quarter'] = last_days.index.quarter
    last_days['month'] = last_days.index.month
    last_days['day'] = last_days.index.day
    
    # Mostrar exemplos por quarter
    for quarter in [1, 2, 3, 4]:
        quarter_data = last_days[last_days['quarter'] == quarter]
        
        if len(quarter_data) > 0:
            if quarter == 1:
                quarter_name = "Q1 (Jan-Mar)"
                expected_month = 3
            elif quarter == 2:
                quarter_name = "Q2 (Abr-Jun)"
                expected_month = 6
            elif quarter == 3:
                quarter_name = "Q3 (Jul-Set)"
                expected_month = 9
            else:
                quarter_name = "Q4 (Out-Dez)"
                expected_month = 12
            
            print(f"\n📊 {quarter_name}:")
            print(f"   Total de últimos dias: {len(quarter_data)}")
            
            # Mostrar alguns exemplos
            examples = quarter_data.head(5)
            for date, row in examples.iterrows():
                print(f"   • {date.strftime('%d/%m/%Y')} ({date.strftime('%A')}) - Mês {row['month']}")
            
            if len(quarter_data) > 5:
                print(f"   ... e mais {len(quarter_data) - 5} datas")
            
            # Verificar se estão no mês correto
            correct_month = quarter_data[quarter_data['month'] == expected_month]
            if len(correct_month) == len(quarter_data):
                print(f"   ✅ Todas as datas estão no mês correto ({expected_month})")
            else:
                print(f"   ⚠️ {len(correct_month)}/{len(quarter_data)} datas no mês correto")
    
    print()
    
    # Verificar distribuição por ano
    print("📈 DISTRIBUIÇÃO POR ANO:")
    print("-" * 40)
    
    year_counts = last_days['year'].value_counts().sort_index()
    for year, count in year_counts.items():
        print(f"   {year}: {count} últimos dias de quarter")
    
    print()
    
    # Verificar se há 4 últimos dias por ano (esperado)
    print("🔍 ANÁLISE DE CONSISTÊNCIA:")
    print("-" * 40)
    
    expected_per_year = 4  # 4 quarters por ano
    years_with_correct_count = sum(1 for count in year_counts if count == expected_per_year)
    
    print(f"   Anos com 4 últimos dias (esperado): {years_with_correct_count}/{len(year_counts)}")
    
    if years_with_correct_count < len(year_counts):
        print("   Anos com contagem diferente:")
        for year, count in year_counts.items():
            if count != expected_per_year:
                print(f"     {year}: {count} dias (esperado: {expected_per_year})")
    
    print()
    
    # Mostrar últimos 10 dias marcados como fim de quarter
    print("📅 ÚLTIMOS 10 DIAS MARCADOS COMO FIM DE QUARTER:")
    print("-" * 60)
    
    recent_last_days = last_days.tail(10)
    for date, row in recent_last_days.iterrows():
        quarter_name = f"Q{row['quarter']}"
        print(f"   {date.strftime('%d/%m/%Y')} ({date.strftime('%A')}) - {quarter_name} de {row['year']}")
    
    print()
    
    # Verificar se 11/07/2025 (hoje) deveria ser marcado
    today = pd.Timestamp('2025-07-11')
    if today in dados.index:
        is_marked = dados.loc[today, 'Last_Day_Quarter']
        print("🎯 VERIFICAÇÃO ESPECÍFICA - 11/07/2025:")
        print("-" * 40)
        print(f"   Data: {today.strftime('%d/%m/%Y')} ({today.strftime('%A')})")
        print(f"   Quarter: Q{today.quarter} ({today.month}/2025)")
        print(f"   Marcado como último dia: {'✅ SIM' if is_marked == 1 else '❌ NÃO'}")
        
        # Verificar se é realmente o último dia de julho nos dados
        july_2025 = dados[dados.index.month == 7][dados.index.year == 2025]
        if len(july_2025) > 0:
            last_july_date = july_2025.index.max()
            print(f"   Último dia de julho nos dados: {last_july_date.strftime('%d/%m/%Y')}")
            print(f"   É o último dia disponível: {'✅ SIM' if today == last_july_date else '❌ NÃO'}")
    
    print()
    print("✅ Verificação concluída!")

if __name__ == "__main__":
    verificar_last_day_quarter()
