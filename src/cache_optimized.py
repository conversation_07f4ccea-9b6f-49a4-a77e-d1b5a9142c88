#!/usr/bin/env python3
"""
Sistema de Cache Otimizado para dados históricos do XGBoost
Usa formato Parquet para melhor performance e compressão
"""

import os
import sys
import pandas as pd
import pyarrow as pa
import pyarrow.parquet as pq
from datetime import datetime, timedelta
import yfinance as yf
from pathlib import Path
import hashlib
import json

# Adicionar o diretório src ao path para importar config
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from config_loader import config, setup_environment

class OptimizedCache:
    """
    Sistema de cache otimizado usando Parquet
    """
    
    def __init__(self):
        self.cache_dir = Path('data/cache/optimized')
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        # Configurações
        self.compression = 'snappy'  # Compressão rápida e eficiente
        self.metadata_file = self.cache_dir / 'cache_metadata.json'
        
    def _get_cache_path(self, ticker: str) -> Path:
        """Retorna o caminho do arquivo de cache para um ticker"""
        ticker_clean = ticker.replace('.SA', '')
        return self.cache_dir / f'{ticker_clean}.parquet'
    
    def _load_metadata(self) -> dict:
        """Carrega metadados do cache"""
        if self.metadata_file.exists():
            with open(self.metadata_file, 'r') as f:
                return json.load(f)
        return {}
    
    def _save_metadata(self, metadata: dict):
        """Salva metadados do cache"""
        with open(self.metadata_file, 'w') as f:
            json.dump(metadata, f, indent=2, default=str)
    
    def _calculate_data_hash(self, data: pd.DataFrame) -> str:
        """Calcula hash dos dados para verificar integridade"""
        return hashlib.md5(str(data.values.tobytes()).encode()).hexdigest()
    
    def get_cached_data(self, ticker: str) -> tuple[pd.DataFrame, dict]:
        """
        Recupera dados do cache
        
        Returns:
            tuple: (dados, info) onde info contém metadados
        """
        cache_path = self._get_cache_path(ticker)
        metadata = self._load_metadata()
        ticker_clean = ticker.replace('.SA', '')
        
        if not cache_path.exists():
            return None, {}
        
        try:
            # Ler dados do Parquet
            data = pd.read_parquet(cache_path)
            data.index = pd.to_datetime(data.index)
            
            # Obter informações do cache
            info = metadata.get(ticker_clean, {})
            info['file_size'] = cache_path.stat().st_size
            info['last_modified'] = datetime.fromtimestamp(cache_path.stat().st_mtime)
            
            print(f"     📁 Cache Parquet encontrado para {ticker} - {len(data)} dias ({info['file_size']/1024:.1f}KB)")
            return data, info
            
        except Exception as e:
            print(f"     ⚠️ Erro ao ler cache Parquet de {ticker}: {e}")
            return None, {}
    
    def save_cached_data(self, ticker: str, data: pd.DataFrame, source: str = "yfinance"):
        """
        Salva dados no cache
        
        Args:
            ticker: Código da ação
            data: DataFrame com dados OHLCV
            source: Fonte dos dados
        """
        cache_path = self._get_cache_path(ticker)
        ticker_clean = ticker.replace('.SA', '')
        
        try:
            # Preparar dados para salvar
            data_to_save = data.copy()
            
            # Salvar em formato Parquet com compressão
            data_to_save.to_parquet(
                cache_path,
                compression=self.compression,
                index=True
            )
            
            # Atualizar metadados
            metadata = self._load_metadata()
            metadata[ticker_clean] = {
                'ticker': ticker,
                'last_update': datetime.now(),
                'data_start': data.index.min(),
                'data_end': data.index.max(),
                'total_days': len(data),
                'source': source,
                'data_hash': self._calculate_data_hash(data),
                'compression': self.compression
            }
            self._save_metadata(metadata)
            
            file_size = cache_path.stat().st_size
            print(f"     💾 Cache Parquet salvo para {ticker} - {len(data)} dias ({file_size/1024:.1f}KB)")
            
        except Exception as e:
            print(f"     ❌ Erro ao salvar cache Parquet de {ticker}: {e}")
    
    def update_cache_with_recent_data(self, ticker: str, nome: str) -> pd.DataFrame:
        """
        Atualiza cache com dados recentes (sempre baixa hoje e ontem)
        """
        # Tentar carregar cache existente
        cached_data, cache_info = self.get_cached_data(ticker)
        
        # Determinar período para download
        hoje = datetime.now().date()
        
        if cached_data is not None and len(cached_data) > 0:
            # Verificar data mais recente no cache
            ultima_data_cache = cached_data.index.max().date()
            
            # SEMPRE baixar dados do dia atual e anterior
            dias_para_baixar = max(2, (hoje - ultima_data_cache).days + 1)
            data_inicio = hoje - timedelta(days=dias_para_baixar + 1)
            
            print(f"     🔄 Atualizando cache Parquet de {ticker} - baixando últimos {dias_para_baixar} dias")
            
            # Baixar dados recentes
            dados_recentes = yf.download(ticker, start=data_inicio, progress=False)
            
            if dados_recentes is not None and len(dados_recentes) > 0:
                # Corrigir MultiIndex se necessário
                if isinstance(dados_recentes.columns, pd.MultiIndex):
                    dados_recentes.columns = dados_recentes.columns.droplevel(1)
                
                # Combinar dados
                data_inicio_ts = pd.Timestamp(data_inicio)
                dados_combinados = cached_data[cached_data.index < data_inicio_ts]
                dados_combinados = pd.concat([dados_combinados, dados_recentes])
                
                # Remover duplicatas e ordenar
                dados_combinados = dados_combinados[~dados_combinados.index.duplicated(keep='last')]
                dados_combinados = dados_combinados.sort_index()
                
                # Salvar cache atualizado
                self.save_cached_data(ticker, dados_combinados)
                
                nova_ultima_data = dados_combinados.index.max().date()
                print(f"     ✅ Cache Parquet atualizado para {ticker} - última: {nova_ultima_data}")
                
                return dados_combinados
            else:
                print(f"     ⚠️ Falha ao baixar dados recentes de {ticker}, usando cache existente")
                return cached_data
        else:
            # Não há cache, baixar dados completos
            periodo = config.get('xgboost.data_period')
            print(f"     📊 Baixando dados completos de {ticker} ({nome}) - período: {periodo}")
            
            dados = yf.download(ticker, period=periodo, progress=False)
            
            if dados is not None and len(dados) > 0:
                # Corrigir MultiIndex se necessário
                if isinstance(dados.columns, pd.MultiIndex):
                    dados.columns = dados.columns.columns.droplevel(1)
                
                # Salvar no cache
                self.save_cached_data(ticker, dados)
                
                return dados
            else:
                print(f"     ❌ Falha ao baixar dados de {ticker}")
                return None
    
    def get_cache_stats(self) -> dict:
        """Retorna estatísticas do cache"""
        metadata = self._load_metadata()
        
        if not metadata:
            return {'total_files': 0, 'total_size': 0}
        
        total_size = 0
        for file_path in self.cache_dir.glob('*.parquet'):
            total_size += file_path.stat().st_size
        
        return {
            'total_files': len(metadata),
            'total_size': total_size,
            'total_size_mb': total_size / (1024 * 1024),
            'compression': self.compression,
            'cache_dir': str(self.cache_dir)
        }
    
    def clear_cache(self):
        """Remove todos os arquivos de cache"""
        import shutil
        if self.cache_dir.exists():
            shutil.rmtree(self.cache_dir)
            print("🗑️ Cache Parquet otimizado limpo")
        else:
            print("📁 Nenhum cache Parquet encontrado para limpar")

# Instância global do cache otimizado
optimized_cache = OptimizedCache()
