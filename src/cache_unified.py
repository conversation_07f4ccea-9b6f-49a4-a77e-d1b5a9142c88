#!/usr/bin/env python3
"""
Sistema de Cache Unificado - Todas as ações em um único arquivo
Atualização em lote para máxima eficiência
"""

import os
import sys
import pandas as pd
import pyarrow as pa
import pyarrow.parquet as pq
from datetime import datetime, timedelta
import yfinance as yf
from pathlib import Path
import json
from typing import List, Dict, Tuple
import time

# Adicionar o diretório src ao path para importar config
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from config_loader import config, setup_environment

class UnifiedCache:
    """
    Sistema de cache unificado - todas as ações em um arquivo único
    """
    
    def __init__(self):
        self.cache_dir = Path('data/cache/unified')
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        # Arquivos principais
        self.unified_file = self.cache_dir / 'all_stocks_data.parquet'
        self.metadata_file = self.cache_dir / 'unified_metadata.json'
        
        # Configurações
        self.compression = 'snappy'
        
    def _load_metadata(self) -> dict:
        """Carrega metadados do cache unificado"""
        if self.metadata_file.exists():
            with open(self.metadata_file, 'r') as f:
                return json.load(f)
        return {
            'last_update': None,
            'tickers': {},
            'total_records': 0,
            'date_range': {'start': None, 'end': None}
        }
    
    def _save_metadata(self, metadata: dict):
        """Salva metadados do cache unificado"""
        with open(self.metadata_file, 'w') as f:
            json.dump(metadata, f, indent=2, default=str)
    
    def load_unified_data(self) -> Tuple[pd.DataFrame, dict]:
        """
        Carrega dados unificados do cache
        
        Returns:
            tuple: (dataframe_unificado, metadados)
        """
        if not self.unified_file.exists():
            return None, {}
        
        try:
            # Ler dados do Parquet
            data = pd.read_parquet(self.unified_file)
            metadata = self._load_metadata()
            
            file_size = self.unified_file.stat().st_size
            print(f"     📁 Cache unificado encontrado - {len(data)} registros ({file_size/1024/1024:.1f}MB)")
            
            return data, metadata
            
        except Exception as e:
            print(f"     ⚠️ Erro ao ler cache unificado: {e}")
            return None, {}
    
    def save_unified_data(self, data: pd.DataFrame, tickers_info: Dict[str, dict]):
        """
        Salva dados unificados no cache
        
        Args:
            data: DataFrame com dados de todas as ações
            tickers_info: Informações sobre cada ticker
        """
        try:
            # Salvar em formato Parquet com compressão
            data.to_parquet(
                self.unified_file,
                compression=self.compression,
                index=True
            )
            
            # Atualizar metadados
            metadata = {
                'last_update': datetime.now(),
                'tickers': tickers_info,
                'total_records': len(data),
                'date_range': {
                    'start': data.index.min(),
                    'end': data.index.max()
                },
                'compression': self.compression,
                'columns': list(data.columns)
            }
            self._save_metadata(metadata)
            
            file_size = self.unified_file.stat().st_size
            print(f"     💾 Cache unificado salvo - {len(data)} registros ({file_size/1024/1024:.1f}MB)")
            
        except Exception as e:
            print(f"     ❌ Erro ao salvar cache unificado: {e}")
    
    def batch_download_recent_data(self, tickers: List[str]) -> Dict[str, pd.DataFrame]:
        """
        Baixa dados recentes para múltiplas ações em lote
        MUITO mais eficiente que downloads individuais
        
        Args:
            tickers: Lista de tickers para baixar
            
        Returns:
            Dict com dados de cada ticker
        """
        print(f"     🚀 Download em lote para {len(tickers)} ações...")
        
        # Determinar período para download (sempre últimos 3 dias para garantia)
        hoje = datetime.now().date()
        data_inicio = hoje - timedelta(days=3)
        
        # Download em lote usando yfinance (muito mais eficiente)
        start_time = time.time()
        
        try:
            # yfinance pode baixar múltiplas ações de uma vez
            tickers_str = ' '.join(tickers)
            dados_lote = yf.download(tickers_str, start=data_inicio, progress=False, group_by='ticker')
            
            download_time = time.time() - start_time
            print(f"     ⚡ Download em lote concluído em {download_time:.2f}s")
            
            # Processar dados por ticker
            dados_por_ticker = {}
            
            if len(tickers) == 1:
                # Caso especial: apenas um ticker
                ticker = tickers[0]
                if dados_lote is not None and len(dados_lote) > 0:
                    dados_por_ticker[ticker] = dados_lote
            else:
                # Múltiplos tickers
                for ticker in tickers:
                    try:
                        if ticker in dados_lote.columns.levels[0]:
                            ticker_data = dados_lote[ticker]
                            if len(ticker_data.dropna()) > 0:
                                dados_por_ticker[ticker] = ticker_data
                    except Exception as e:
                        print(f"     ⚠️ Erro ao processar {ticker}: {e}")
            
            print(f"     ✅ Processados {len(dados_por_ticker)}/{len(tickers)} tickers com sucesso")
            return dados_por_ticker
            
        except Exception as e:
            print(f"     ❌ Erro no download em lote: {e}")
            return {}
    
    def update_unified_cache(self, all_tickers: List[Tuple[str, str]]) -> pd.DataFrame:
        """
        Atualiza cache unificado com dados recentes
        Faz download em lote e atualiza tudo de uma vez
        
        Args:
            all_tickers: Lista de tuplas (ticker, nome)
            
        Returns:
            DataFrame unificado atualizado
        """
        print("🔄 Atualizando cache unificado...")
        
        # Carregar cache existente
        cached_data, metadata = self.load_unified_data()
        
        # Extrair apenas os tickers (all_tickers pode ter 2 ou 3 elementos por tupla)
        tickers = [ticker for ticker in [item[0] for item in all_tickers]]
        
        if cached_data is not None and len(cached_data) > 0:
            # Cache existe - fazer download em lote dos dados recentes
            print("     📊 Cache existente encontrado - fazendo atualização incremental")
            
            # Download em lote dos dados recentes
            dados_recentes = self.batch_download_recent_data(tickers)
            
            if dados_recentes:
                # Combinar dados existentes com dados recentes
                print("     🔄 Combinando dados históricos com dados recentes...")
                
                # Determinar data de corte para evitar duplicatas
                hoje = datetime.now().date()
                data_corte = hoje - timedelta(days=3)
                data_corte_ts = pd.Timestamp(data_corte)
                
                # Estratégia: manter todos os dados históricos e apenas atualizar os últimos dias
                dados_combinados = cached_data.copy()

                for ticker in tickers:
                    if ticker in dados_recentes:
                        ticker_recente = dados_recentes[ticker]

                        # Adicionar prefixo do ticker às colunas
                        ticker_recente.columns = [f"{ticker}_{col}" for col in ticker_recente.columns]

                        # Atualizar apenas as datas dos dados recentes
                        for col in ticker_recente.columns:
                            if col in dados_combinados.columns:
                                # Atualizar valores existentes para as datas dos dados recentes
                                dados_combinados.loc[ticker_recente.index, col] = ticker_recente[col]
                            else:
                                # Adicionar nova coluna se não existir
                                dados_combinados[col] = ticker_recente[col]
                
                # Remover duplicatas e ordenar
                dados_combinados = dados_combinados.loc[~dados_combinados.index.duplicated(keep='last')]
                dados_combinados = dados_combinados.sort_index()
                
                # Atualizar informações dos tickers
                tickers_info = {}
                for item in all_tickers:
                    ticker = item[0]
                    nome = item[1] if len(item) > 1 else ticker
                    tickers_info[ticker] = {
                        'name': nome,
                        'columns': [col for col in dados_combinados.columns if col.startswith(f"{ticker}_")],
                        'last_date': dados_combinados.index.max(),
                        'total_records': len(dados_combinados)
                    }
                
                # Salvar cache atualizado
                self.save_unified_data(dados_combinados, tickers_info)
                
                print(f"     ✅ Cache unificado atualizado - {len(dados_combinados)} registros")
                return dados_combinados
            else:
                print("     ⚠️ Falha no download em lote, usando cache existente")
                return cached_data
        
        else:
            # Não há cache - criar do zero com dados completos
            print("     📊 Criando cache unificado do zero...")
            
            periodo = config.get('xgboost.data_period', '15y')
            print(f"     📅 Baixando {periodo} de dados para {len(tickers)} ações...")
            
            # Download em lote de dados completos
            start_time = time.time()
            
            try:
                tickers_str = ' '.join(tickers)
                dados_completos = yf.download(tickers_str, period=periodo, progress=False, group_by='ticker')
                
                download_time = time.time() - start_time
                print(f"     ⚡ Download completo em {download_time:.2f}s")
                
                if dados_completos is not None and len(dados_completos) > 0:
                    # Processar dados para formato unificado
                    if len(tickers) == 1:
                        # Caso especial: apenas um ticker
                        ticker = tickers[0]
                        dados_completos.columns = [f"{ticker}_{col}" for col in dados_completos.columns]
                        dados_unificados = dados_completos
                    else:
                        # Múltiplos tickers - dados já vêm com MultiIndex
                        # Achatar MultiIndex para formato ticker_coluna
                        dados_unificados = pd.DataFrame(index=dados_completos.index)
                        
                        for ticker in tickers:
                            if ticker in dados_completos.columns.levels[0]:
                                ticker_data = dados_completos[ticker]
                                for col in ticker_data.columns:
                                    dados_unificados[f"{ticker}_{col}"] = ticker_data[col]
                    
                    # Criar informações dos tickers
                    tickers_info = {}
                    for item in all_tickers:
                        ticker = item[0]
                        nome = item[1] if len(item) > 1 else ticker
                        ticker_cols = [col for col in dados_unificados.columns if col.startswith(f"{ticker}_")]
                        if ticker_cols:
                            tickers_info[ticker] = {
                                'name': nome,
                                'columns': ticker_cols,
                                'last_date': dados_unificados.index.max(),
                                'total_records': len(dados_unificados)
                            }
                    
                    # Salvar cache inicial
                    self.save_unified_data(dados_unificados, tickers_info)
                    
                    print(f"     ✅ Cache unificado criado - {len(dados_unificados)} registros")
                    return dados_unificados
                
            except Exception as e:
                print(f"     ❌ Erro no download completo: {e}")
                return None
        
        return None
    
    def get_ticker_data(self, unified_data: pd.DataFrame, ticker: str) -> pd.DataFrame:
        """
        Extrai dados de um ticker específico do DataFrame unificado
        
        Args:
            unified_data: DataFrame unificado
            ticker: Ticker desejado
            
        Returns:
            DataFrame com dados do ticker (colunas OHLCV padrão)
        """
        # Encontrar colunas do ticker
        ticker_cols = [col for col in unified_data.columns if col.startswith(f"{ticker}_")]
        
        if not ticker_cols:
            return None
        
        # Extrair dados do ticker
        ticker_data = unified_data[ticker_cols].copy()
        
        # Renomear colunas removendo o prefixo do ticker
        ticker_data.columns = [col.replace(f"{ticker}_", "") for col in ticker_data.columns]
        
        # Remover linhas com todos os valores NaN
        ticker_data = ticker_data.dropna(how='all')

        # Debug: verificar se temos dados suficientes
        if len(ticker_data) < 100:
            print(f"     ⚠️ {ticker} tem apenas {len(ticker_data)} dias de dados")

        return ticker_data
    
    def get_cache_stats(self) -> dict:
        """Retorna estatísticas do cache unificado"""
        if not self.unified_file.exists():
            return {'exists': False}
        
        metadata = self._load_metadata()
        file_size = self.unified_file.stat().st_size
        
        return {
            'exists': True,
            'file_size': file_size,
            'file_size_mb': file_size / (1024 * 1024),
            'total_records': metadata.get('total_records', 0),
            'total_tickers': len(metadata.get('tickers', {})),
            'last_update': metadata.get('last_update'),
            'date_range': metadata.get('date_range', {}),
            'compression': metadata.get('compression', 'unknown')
        }
    
    def clear_cache(self):
        """Remove cache unificado"""
        if self.unified_file.exists():
            self.unified_file.unlink()
        if self.metadata_file.exists():
            self.metadata_file.unlink()
        print("🗑️ Cache unificado limpo")

# Instância global do cache unificado
unified_cache = UnifiedCache()
