#!/usr/bin/env python3
"""
Script para verificar a importância das features dos quarters no modelo XGBoost
"""

import pickle
import pandas as pd
import numpy as np
import os

def verificar_quarters_features():
    """
    Carrega o modelo XGBoost e verifica a importância das features dos quarters
    """
    
    # Carregar modelo salvo
    modelo_path = 'results/models/xgboost_analysis/modelo_binario.pkl'
    
    if not os.path.exists(modelo_path):
        print(f"❌ Modelo não encontrado em: {modelo_path}")
        return
    
    print("📊 VERIFICAÇÃO DAS FEATURES DOS QUARTERS")
    print("=" * 60)
    
    # Carregar modelo
    with open(modelo_path, 'rb') as f:
        dados_modelo = pickle.load(f)
    
    modelo = dados_modelo['modelo']
    feature_cols = dados_modelo['feature_cols']
    
    # Obter importâncias das features
    importancias = modelo.feature_importances_
    
    # Criar DataFrame com features e importâncias
    df_importancia = pd.DataFrame({
        'feature': feature_cols,
        'importance': importancias
    }).sort_values('importance', ascending=False)
    
    print(f"📋 Total de features: {len(feature_cols)}")
    print(f"🎯 Acurácia do modelo: {dados_modelo['accuracy']:.3f}")
    print()
    
    # Verificar features dos quarters
    quarters_features = ['Quarter_1', 'Quarter_2', 'Quarter_3', 'Quarter_4', 'Last_Day_Quarter']
    
    print("🗓️ IMPORTÂNCIA DAS FEATURES DOS QUARTERS:")
    print("-" * 50)
    
    for quarter in quarters_features:
        if quarter in df_importancia['feature'].values:
            importancia = df_importancia[df_importancia['feature'] == quarter]['importance'].iloc[0]
            ranking = df_importancia[df_importancia['feature'] == quarter].index[0] + 1
            
            # Determinar o período do quarter
            if quarter == 'Quarter_1':
                periodo = "Jan-Mar"
            elif quarter == 'Quarter_2':
                periodo = "Abr-Jun"
            elif quarter == 'Quarter_3':
                periodo = "Jul-Set"
            elif quarter == 'Quarter_4':
                periodo = "Out-Dez"
            else:
                periodo = "Último dia de quarter"

            print(f"   {quarter} ({periodo}): {importancia:.6f} (#{ranking} de {len(feature_cols)})")
        else:
            print(f"   {quarter}: ❌ Não encontrada")
    
    print()
    
    # Mostrar top 15 features mais importantes
    print("🏆 TOP 15 FEATURES MAIS IMPORTANTES:")
    print("-" * 50)
    
    for i, (_, row) in enumerate(df_importancia.head(15).iterrows(), 1):
        feature = row['feature']
        importance = row['importance']
        
        # Destacar se é uma feature de quarter
        if feature in quarters_features:
            print(f"   {i:2d}. {feature:<30} {importance:.6f} ⭐ QUARTER")
        else:
            print(f"   {i:2d}. {feature:<30} {importance:.6f}")
    
    print()
    
    # Comparar com outras features temporais
    print("📅 COMPARAÇÃO COM OUTRAS FEATURES TEMPORAIS:")
    print("-" * 50)
    
    # Features de dias da semana
    weekday_features = ['Segunda', 'Terca', 'Quarta', 'Quinta', 'Sexta']
    print("   Dias da semana:")
    for feature in weekday_features:
        if feature in df_importancia['feature'].values:
            importancia = df_importancia[df_importancia['feature'] == feature]['importance'].iloc[0]
            ranking = df_importancia[df_importancia['feature'] == feature].index[0] + 1
            print(f"     {feature}: {importancia:.6f} (#{ranking})")
    
    print()
    
    # Features de meses
    print("   Meses (top 5):")
    month_features = [f'Mes_{i}' for i in range(1, 13)]
    month_importances = []
    
    for feature in month_features:
        if feature in df_importancia['feature'].values:
            importancia = df_importancia[df_importancia['feature'] == feature]['importance'].iloc[0]
            ranking = df_importancia[df_importancia['feature'] == feature].index[0] + 1
            month_importances.append((feature, importancia, ranking))
    
    # Ordenar por importância e mostrar top 5
    month_importances.sort(key=lambda x: x[1], reverse=True)
    for feature, importancia, ranking in month_importances[:5]:
        print(f"     {feature}: {importancia:.6f} (#{ranking})")
    
    print()
    
    # Estatísticas das features dos quarters
    quarters_importances = []
    for quarter in quarters_features:
        if quarter in df_importancia['feature'].values:
            importancia = df_importancia[df_importancia['feature'] == quarter]['importance'].iloc[0]
            quarters_importances.append(importancia)
    
    if quarters_importances:
        print("📊 ESTATÍSTICAS DAS FEATURES DOS QUARTERS:")
        print("-" * 50)
        print(f"   Importância média: {np.mean(quarters_importances):.6f}")
        print(f"   Importância máxima: {np.max(quarters_importances):.6f}")
        print(f"   Importância mínima: {np.min(quarters_importances):.6f}")
        print(f"   Desvio padrão: {np.std(quarters_importances):.6f}")
    
    print()
    print("✅ Verificação concluída!")

if __name__ == "__main__":
    verificar_quarters_features()
