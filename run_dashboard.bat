@echo off
REM Script para executar o Dashboard da Carteira de Investimentos (Windows)
REM Uso: run_dashboard.bat

echo 📈 Dashboard - Análise Temporal da Carteira de Investimentos
echo ============================================================

REM Verificar se o arquivo de dados existe
if not exist "results\evolucao_carteira_temporal.csv" (
    echo ⚠️  Dados da evolução temporal não encontrados.
    echo 🔄 Executando análise temporal primeiro...
    uv run python src\analise_carteira_temporal.py
    
    if errorlevel 1 (
        echo ❌ Erro ao executar análise temporal.
        pause
        exit /b 1
    )
    echo ✅ Análise temporal concluída!
)

echo 🚀 Iniciando dashboard...
echo 📱 O dashboard será aberto em: http://localhost:8501
echo ⏹️  Para parar o dashboard, pressione Ctrl+C
echo.

REM Executar o dashboard
uv run streamlit run dashboard_carteira.py

pause
