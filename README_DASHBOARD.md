# 📊 TRADING DASHBOARD - Professional Portfolio Analysis

Este dashboard interativo foi criado com **Streamlit** e **Plotly** no **estilo profissional usado por traders**, com tema escuro, cores do mercado financeiro e terminologia técnica. Baseado no script `analise_carteira_temporal.py` para análise temporal da carteira de investimentos.

## 🚀 Como Executar o Dashboard

### 1. Pré-requisitos
Certifique-se de que as dependências estão instaladas:
```bash
uv add streamlit plotly
```

### 2. Executar a Análise Temporal (se necessário)
Primeiro, execute a análise temporal para gerar os dados:
```bash
uv run python src/analise_carteira_temporal.py
```

### 3. Iniciar o Dashboard
Execute o dashboard:
```bash
uv run streamlit run dashboard_carteira.py
```

O dashboard será aberto automaticamente no navegador em `http://localhost:8501`

## 📊 Funcionalidades do Trading Dashboard

### 🎯 Portfolio Overview (Estilo Trader Profissional)
- **INITIAL CAPITAL**: Capital inicial investido
- **TOTAL EQUITY**: Valor total atual da carteira com P&L
- **P&L (%)**: Profit & Loss percentual com indicadores visuais
- **CASH AVAILABLE**: Capital disponível para novas posições
- **POSITIONS VALUE**: Valor atual das posições em ações

### 🎨 Design Profissional
- **Tema Escuro**: Fundo preto/cinza escuro (#0e1117, #1e2329)
- **Cores de Trading**: Verde (#00d4aa) para lucros, Vermelho (#ff6b6b) para perdas
- **Cor Destaque**: Laranja dourado (#f7931e) para elementos importantes
- **Terminologia**: Inglês técnico usado no mercado financeiro

### 📈 Gráficos Interativos Estilo Trading

#### 1. EQUITY CURVE & PERFORMANCE
- **Equity Curve**: Evolução do capital com linha principal destacada
- **P&L Percentage**: Gráfico de rendimento com área preenchida
- **Cores Dinâmicas**: Verde para lucro, vermelho para prejuízo
- **Tema Escuro**: Fundo escuro com grid sutil
- **Hover Profissional**: Informações detalhadas ao passar o mouse

#### 2. TRADING ACTIVITY
- **TRADE SUMMARY**: BUY/SELL orders com métricas de performance
- **WIN RATE**: Taxa de sucesso simulada baseada no rendimento
- **TOP HOLDINGS**: Ranking das principais posições
- **PORTFOLIO ALLOCATION**: Gráfico de pizza com cores profissionais

#### 3. TRADING TIMELINE
- **Trade Execution Timeline**: Scatter plot de todas as operações
- **Diferenciação Visual**: BUY (verde), SELL (vermelho)
- **Tamanho Proporcional**: Baseado no valor da transação
- **Símbolos Limpos**: Tickers sem .SA para melhor visualização

### ⚙️ Trading Controls (Controles Estilo Trader)

#### TIME FRAME (Sidebar)
- **Botões Rápidos**: 7D, 1M, 3M, ALL para seleção rápida de período
- **📅 Custom Date Range**: Seletores de data personalizados
- **🔄 REFRESH DATA**: Botão para atualizar dados em tempo real

#### SYSTEM CONTROLS
- **📊 MARKET STATUS**: Indicador de mercado aberto/fechado
- **Last Update**: Timestamp da última atualização
- **💡 TRADING TIPS**: Dicas profissionais de uso

#### DETAILED DATA (Abas Profissionais)
- **📈 EQUITY DATA**: Dados da evolução com colunas em inglês
- **💼 TRADE LOG**: Histórico completo de operações formatado

## 🎨 Características Visuais

### Cores e Estilo
- **Verde**: Lucros, compras, tendências positivas
- **Vermelho**: Prejuízos, vendas, tendências negativas
- **Azul**: Capital disponível
- **Laranja**: Valor das ações
- **Roxo**: Rendimento percentual

### Responsividade
- Layout adaptável para diferentes tamanhos de tela
- Gráficos redimensionáveis
- Interface otimizada para desktop e tablet

## 📋 Estrutura dos Dados

### Dados de Entrada
- **carteira.csv**: Arquivo com transações da carteira
- **results/evolucao_carteira_temporal.csv**: Dados da evolução temporal

### Colunas Principais
- `data`: Data da análise
- `capital_inicial`: Capital inicial
- `capital_disponivel`: Capital disponível
- `valor_atual_acoes`: Valor atual das ações
- `capital_total`: Capital total
- `rendimento_capital_percentual`: Rendimento em %

## 🔧 Personalização

### Modificar Período de Análise
Use os filtros de data na sidebar para focar em períodos específicos.

### Atualizar Dados
1. Clique em "🔄 Atualizar Análise" na sidebar
2. Ou execute novamente `analise_carteira_temporal.py`

### Adicionar Novas Métricas
Edite a função `criar_dashboard()` em `analise_carteira_temporal.py` para adicionar:
- Novas métricas no resumo executivo
- Gráficos adicionais
- Filtros personalizados

## 🚨 Solução de Problemas

### Dashboard não carrega
1. Verifique se `results/evolucao_carteira_temporal.csv` existe
2. Execute primeiro `analise_carteira_temporal.py`
3. Verifique se todas as dependências estão instaladas

### Dados não aparecem
1. Verifique se `carteira.csv` existe e tem dados válidos
2. Confirme se as datas estão no formato correto
3. Execute a análise temporal novamente

### Gráficos não interativos
1. Verifique se Plotly está instalado: `uv add plotly`
2. Limpe o cache do Streamlit: Ctrl+C e reinicie

## 📁 Arquivos Relacionados

- `dashboard_carteira.py`: Script principal do dashboard
- `src/analise_carteira_temporal.py`: Análise temporal e função do dashboard
- `carteira.csv`: Dados da carteira
- `config.yaml`: Configurações do sistema
- `results/evolucao_carteira_temporal.csv`: Dados processados

## 💡 Dicas de Uso

1. **Análise de Períodos**: Use os filtros de data para analisar períodos específicos
2. **Hover nos Gráficos**: Passe o mouse sobre os pontos para ver detalhes
3. **Zoom**: Use as ferramentas de zoom do Plotly para focar em áreas específicas
4. **Exportar Dados**: Os dados podem ser copiados das tabelas para análises externas
5. **Atualização Automática**: O dashboard detecta automaticamente mudanças nos dados

## 🔄 Atualizações Futuras

Possíveis melhorias:
- Comparação com benchmarks (Ibovespa, CDI)
- Análise de risco (volatilidade, drawdown)
- Projeções futuras
- Alertas automáticos
- Exportação de relatórios em PDF
