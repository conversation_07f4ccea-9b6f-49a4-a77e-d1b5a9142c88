[project]
name = "finance"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "alpha-vantage>=3.0.0",
    "diskcache>=5.6.3",
    "holidays>=0.76",
    "matplotlib>=3.10.3",
    "numpy>=1.26.0,<2.0.0",
    "pandas>=2.3.0",
    "plotly>=6.2.0",
    "psutil>=7.0.0",
    "pyarrow>=20.0.0",
    "pyyaml>=6.0.2",
    "scikit-learn>=1.7.0",
    "scipy>=1.15.3",
    "seaborn>=0.13.2",
    "streamlit>=1.46.1",
    "tensorflow>=2.16.1,<2.18.0",
    "twelvedata>=1.2.25",
    "xgboost>=3.0.2",
    "yfinance>=0.2.63",
]
