# Sistema de Cache Histórico - XGBoost

Este sistema otimiza o download de dados históricos armazenando dados já baixados e atualizando apenas os dados mais recentes.

## 🚀 Benefícios

- **Velocidade**: Reduz drasticamente o tempo de execução após o primeiro download
- **Eficiência**: Baixa apenas dados dos últimos dias ao invés de todo o histórico
- **Confiabilidade**: Sempre garante que os dados do dia atual e anterior estejam atualizados
- **Flexibilidade**: Configurável via `config.yaml`

## ⚙️ Configuração

No arquivo `config.yaml`, seção `xgboost.cache`:

```yaml
xgboost:
  cache:
    use_cache: true              # Habilitar cache (true/false)
    force_download: false        # Forçar download completo (true/false)
    max_days_outdated: 3         # Máximo de dias desatualizados antes de forçar atualização
```

### Opções de Configuração

- **`use_cache`**: 
  - `true`: Usa cache otimizado (padrão)
  - `false`: Sempre baixa dados completos

- **`force_download`**: 
  - `true`: Ignora cache e baixa dados completos
  - `false`: Usa cache quando disponível (padrão)

- **`max_days_outdated`**: 
  - Número de dias que o cache pode estar desatualizado antes de ser considerado inválido
  - Padrão: 3 dias

## 🔧 Gerenciamento do Cache

### Usando o Gerenciador de Cache

```bash
# Verificar status do cache
uv run python src/cache_manager.py status

# Limpar cache
uv run python src/cache_manager.py clear

# Ver configurações atuais
uv run python src/cache_manager.py config

# Ajuda
uv run python src/cache_manager.py help
```

### Usando o Script Principal

```bash
# Verificar status do cache
uv run python src/classificador_xgboost_sinais.py --cache-status

# Limpar cache
uv run python src/classificador_xgboost_sinais.py --clear-cache

# Executar normalmente (usa configurações do config.yaml)
uv run python src/classificador_xgboost_sinais.py
```

## 📁 Estrutura do Cache

Os dados são armazenados em:
```
data/cache/historical_data/
├── PETR4_historical.csv
├── VALE3_historical.csv
├── ITUB4_historical.csv
└── ...
```

Cada arquivo contém:
- Dados históricos completos da ação
- Índice de datas
- Colunas OHLCV (Open, High, Low, Close, Volume)

## 🔄 Como Funciona

1. **Primeira execução**: Baixa dados completos e salva no cache
2. **Execuções subsequentes**:
   - Verifica se cache existe
   - **SEMPRE baixa dados do dia atual e anterior** (independentemente do cache)
   - Combina dados históricos do cache com dados recentes baixados
   - Atualiza cache com dados mais recentes
3. **Garantia de atualização**: **OBRIGATORIAMENTE** baixa dados do dia atual e anterior a cada execução

## 📊 Exemplo de Uso

### Cenário 1: Primeira execução
```bash
uv run python src/classificador_xgboost_sinais.py
```
- Baixa 15 anos de dados para todas as ações
- Salva no cache
- Tempo: ~5-10 minutos

### Cenário 2: Execução no dia seguinte
```bash
uv run python src/classificador_xgboost_sinais.py
```
- Usa cache existente para dados históricos
- **SEMPRE baixa dados do dia atual e anterior**
- Combina cache + dados recentes
- Tempo: ~30 segundos

### Cenário 3: Forçar atualização completa
```yaml
# config.yaml
xgboost:
  cache:
    force_download: true
```
```bash
uv run python src/classificador_xgboost_sinais.py
```

## 🛠️ Solução de Problemas

### Cache corrompido
```bash
uv run python src/cache_manager.py clear
uv run python src/classificador_xgboost_sinais.py
```

### Dados desatualizados
```bash
# Verificar status
uv run python src/cache_manager.py status

# Se necessário, limpar e recriar
uv run python src/cache_manager.py clear
```

### Desabilitar cache temporariamente
```yaml
# config.yaml
xgboost:
  cache:
    use_cache: false
```

## 💡 Dicas de Performance

1. **Primeira execução**: Execute em horário de menor volatilidade do mercado
2. **Execuções diárias**: Configure para executar automaticamente após o fechamento do mercado
3. **Monitoramento**: Use `cache_manager.py status` para monitorar saúde do cache
4. **Limpeza periódica**: Limpe cache mensalmente para remover dados desnecessários

## 🔍 Logs e Monitoramento

O sistema exibe logs detalhados:
- `📁 Cache encontrado`: Cache existe e será usado para dados históricos
- `🔄 Atualizando cache - baixando últimos X dias (sempre inclui hoje e ontem)`: Baixando dados recentes obrigatórios
- `💾 Cache atualizado`: Cache foi salvo com dados mais recentes
- `💾 Cache criado`: Novo cache criado com dados completos
- `⚠️ Erro ao ler cache`: Problema no arquivo de cache
- `⚠️ Falha ao baixar dados recentes, usando cache existente`: Fallback para cache quando download falha
